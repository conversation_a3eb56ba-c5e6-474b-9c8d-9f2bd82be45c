exports.up = function (knex) {
  return knex.schema
    .createTable('disparos_pixel', (table) => {
      table.increments('cd_disparo').primary();
      table.string('ds_disparo', 200).notNullable();
      table.string('ds_url', 300).notNullable();
      table.string('nr_hash', 200).notNullable();
      table.boolean('in_ativo').defaultTo(true);
      table.integer('cd_usucad').nullable();
      table.timestamp('dt_cadastro').nullable();
      table.timestamps(true, true);
    })
    .createTable('cliques', (table) => {
      table.increments('id').primary();
      table.integer('cd_disparo').references('disparos_pixel.cd_disparo');
      table.string('utm_source').notNullable();
      table.timestamp('data_clique').defaultTo(knex.fn.now());
    })
    .createTable('leads', (table) => {
      table.increments('id').primary();
      table.integer('cd_disparo').references('disparos_pixel.cd_disparo');
      table.string('numero').notNullable();
      table.text('mensagem').notNullable();
      table.timestamp('data_mensagem').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
  return knex.schema
    .dropTableIfExists('leads')
    .dropTableIfExists('cliques')
    .dropTableIfExists('disparos_pixel');
};