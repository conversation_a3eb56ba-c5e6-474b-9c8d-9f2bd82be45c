const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const DisparoPixel = require('../models/disparoPixel');
const Clique = require('../models/clique');
const Lead = require('../models/lead');

// Criar um disparo
router.post('/disparos', async (req, res) => {
  try {
    const { ds_disparo, utm_source } = req.body;
    const nr_hash = crypto.randomBytes(16).toString('hex');
    const baseUrl = process.env.BASE_URL || 'https://real-llamas-burn.loca.lt';
    const ds_url = `${baseUrl}/api/cliques?cd_disparo=${nr_hash}&utm_source=${utm_source || 'facebook'}`;
    const [disparo] = await DisparoPixel.create({
      ds_disparo,
      ds_url,
      nr_hash,
      in_ativo: true,
      cd_usucad: null, // Substitua por req.user.id se houver autenticação
      dt_cadastro: new Date()
    });
    res.status(201).json({ disparo, link: ds_url });
  } catch (error) {
    console.error('Erro ao criar disparo:', error);
    res.status(500).json({ error: 'Erro ao criar disparo' });
  }
});

// Capturar clique (página intermediária)
router.get('/cliques', async (req, res) => {
  try {
    const { cd_disparo, utm_source } = req.query;
    const disparo = await DisparoPixel.findByHash(cd_disparo);
    if (!disparo) {
      return res.status(404).send('Disparo não encontrado');
    }
    await Clique.create({
      cd_disparo: disparo.cd_disparo,
      utm_source: utm_source || 'facebook',
      data_clique: new Date()
    });
    const mensagem = encodeURIComponent(`Quero saber mais sobre ${disparo.ds_disparo}! ID: ${disparo.cd_disparo}`);
    console.log(mensagem);
    res.redirect(`https://wa.me/5521992685661?text=${mensagem}`);
  } catch (error) {
    console.error('Erro ao registrar clique:', error);
    res.status(500).send('Erro ao registrar clique');
  }
});

// Webhook para mensagens do WhatsApp
router.post('/webhook', async (req, res) => {
  try {
    const { event, data } = req.body;
    if (event === 'MESSAGE_RECEIVED') {
      const { from, body } = data; // Ajuste conforme o formato da Evolution API
      const campanha_id = body.match(/ID: (\\d+)/)?.[1];
      await Lead.create({
        cd_disparo: campanha_id ? parseInt(campanha_id) : null,
        numero: from,
        mensagem: body,
        data_mensagem: new Date()
      });
    }
    res.status(200).send('OK');
  } catch (error) {
    console.error('Erro no webhook:', error);
    res.status(500).send('Erro no webhook');
  }
});

// Listar disparos (para testes)
router.get('/disparos', async (req, res) => {
  try {
    const disparos = await DisparoPixel.all();
    res.json(disparos);
  } catch (error) {
    console.error('Erro ao listar disparos:', error);
    res.status(500).json({ error: 'Erro ao listar disparos' });
  }
});

module.exports = router;